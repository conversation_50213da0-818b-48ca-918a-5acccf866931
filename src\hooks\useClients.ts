import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
	clientsApi,
	type CreateClientRequest,
	type UpdateClientRequest,
	type ClientsFilters,
	type Client,
	type ClientsResponse,
	type ClientDetailResponse,
} from "@/lib/api/clientsApi";
import {
	queryKeys,
	defaultMutationOptions,
	mediumLivedQueryOptions,
} from "@/lib/query";
import { useUIStore } from "@/stores/uiStore";
import type { Patient } from "@/components/ui-components/AllPatientListCard";

/**
 * Transform API Client data to Patient interface for UI components
 */
export const transformClientToPatient = (client: Client): Patient => {
	return {
		id: client.id.toString(),
		name: `${client.first_name} ${client.last_name}`,
		email: client.email,
		phone: client.phone_number,
		status: "Active", // Default status, can be enhanced based on client attributes
		lastVisit: new Date(client.updated_at).toLocaleDateString("en-US", {
			day: "2-digit",
			month: "short",
			year: "numeric",
		}),
		syncStatus: "synced", // Default sync status, can be enhanced based on requirements
	};
};

/**
 * Transform API ClientDetail data to PatientDetailsSheet format
 */
export const transformClientDetailToPatientData = (clientDetail: any) => {
	const client = clientDetail.data;

	return {
		name: `${client.first_name} ${client.last_name}`,
		priority: "High", // Could be derived from client attributes
		status: client.is_active ? "Active" : "Inactive",
		email: client.email,
		phone: client.phone_number,
		patientId: client.id.toString(),
		externalId: client.external_id || "",
		profilePictureUrl: client.profile_picture_url,
		lastVisit: client.last_visit
			? new Date(client.last_visit).toLocaleDateString("en-US", {
					day: "2-digit",
					month: "short",
					year: "numeric",
				})
			: "No visits",
		phoneVerified: client.phone_number_verified,
		categories: client.categories?.map((cat: any) => cat.name) || [],
		customIntake:
			client.attributes?.map((attr: any) => ({
				label: attr.label,
				value: attr.value?.toString() || "",
			})) || [],
		forms: [], // This would come from a separate endpoint
		messages: [], // This would come from a separate endpoint
	};
};

/**
 * Hook to fetch clients with pagination and filtering
 * Uses medium-lived query options since client data doesn't change frequently
 */
export const useClients = (
	filters: ClientsFilters = {},
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery<ClientsResponse>(
		queryKeys.clients.list(filters),
		() => clientsApi.getClients(filters),
		{
			...mediumLivedQueryOptions,
			enabled: options?.enabled !== false,
		}
	);
};

/**
 * Hook to fetch a single client by ID with detailed information
 * Uses medium-lived query options since client details don't change frequently
 */
export const useClientDetail = (
	id: string | number,
	options?: {
		enabled?: boolean;
	}
) => {
	return useQuery<ClientDetailResponse>(
		queryKeys.clients.detail(id.toString()),
		() => clientsApi.getClientById(id),
		{
			...mediumLivedQueryOptions,
			enabled: options?.enabled !== false && !!id,
		}
	);
};

/**
 * Hook to create a new client
 * Follows the same pattern as other mutation hooks in the project
 */
export const useCreateClient = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { addToast } = useUIStore();

	return useMutation<any, any, CreateClientRequest>({
		mutationFn: (data: CreateClientRequest) =>
			clientsApi.createClient(data),
		onSuccess: (data: any) => {
			// Show success toast
			addToast({
				type: "success",
				title: "Client Created",
				message: data.message || "Client has been created successfully",
			});
			queryClient.invalidateQueries({
				queryKey: queryKeys.clients.lists(),
			});

			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};

/**
 * Hook to update an existing client
 * Follows the same pattern as useCreateClient
 */
export const useUpdateClient = (options?: {
	onSuccess?: (data: any) => void;
	onError?: (error: any) => void;
}) => {
	const queryClient = useQueryClient();
	const { addToast } = useUIStore();

	return useMutation<
		any,
		any,
		{ id: string | number; data: UpdateClientRequest }
	>({
		mutationFn: ({
			id,
			data,
		}: {
			id: string | number;
			data: UpdateClientRequest;
		}) => clientsApi.updateClient(id, data),
		onSuccess: (data: any) => {
			// Show success toast
			addToast({
				type: "success",
				title: "Client Updated",
				message: "Client information has been updated successfully.",
			});

			// Invalidate and refetch clients list
			queryClient.invalidateQueries({ queryKey: queryKeys.clients.all });

			// Invalidate the specific client detail
			if (data?.data?.id) {
				queryClient.invalidateQueries({
					queryKey: queryKeys.clients.detail(data.data.id.toString()),
				});
			}

			// Call custom onSuccess callback
			options?.onSuccess?.(data);
		},
		onError: (error: any) => {
			// Show error toast
			addToast({
				type: "error",
				title: "Update Failed",
				message:
					error?.message || "Failed to update client information.",
			});

			// Call custom onError callback
			options?.onError?.(error);
		},
		retry: 1,
		retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
	});
};
