/**
 * Query key factories for consistent query key management
 * Follow the pattern: [domain, ...identifiers, ...filters]
 */

export const queryKeys = {
	// Auth related queries
	auth: {
		all: ["auth"] as const,
		user: () => [...queryKeys.auth.all, "user"] as const,
		permissions: () => [...queryKeys.auth.all, "permissions"] as const,
	},

	// Customer related queries
	customers: {
		all: ["customers"] as const,
		lists: () => [...queryKeys.customers.all, "list"] as const,
		list: (filters: Record<string, any>) =>
			[...queryKeys.customers.lists(), filters] as const,
		details: () => [...queryKeys.customers.all, "detail"] as const,
		detail: (id: string) => [...queryKeys.customers.details(), id] as const,
	},

	// Analytics related queries
	analytics: {
		all: ["analytics"] as const,
		dashboard: () => [...queryKeys.analytics.all, "dashboard"] as const,
		reports: () => [...queryKeys.analytics.all, "reports"] as const,
		report: (type: string, filters?: Record<string, any>) =>
			[...queryKeys.analytics.reports(), type, filters] as const,
	},

	// Waitlist related queries
	waitlist: {
		all: ["waitlist"] as const,
		lists: () => [...queryKeys.waitlist.all, "list"] as const,
		list: (filters: Record<string, any>) =>
			[...queryKeys.waitlist.lists(), filters] as const,
		details: () => [...queryKeys.waitlist.all, "detail"] as const,
		detail: (id: string) => [...queryKeys.waitlist.details(), id] as const,
	},

	// Schedule related queries
	schedule: {
		all: ["schedule"] as const,
		events: () => [...queryKeys.schedule.all, "events"] as const,
		event: (id: string) => [...queryKeys.schedule.events(), id] as const,
		calendar: (filters: Record<string, any>) =>
			[...queryKeys.schedule.all, "calendar", filters] as const,
	},

	// Settings related queries
	settings: {
		all: ["settings"] as const,
		profile: () => [...queryKeys.settings.all, "profile"] as const,
		preferences: () => [...queryKeys.settings.all, "preferences"] as const,
		notifications: () =>
			[...queryKeys.settings.all, "notifications"] as const,
	},

	// Locations related queries
	locations: {
		all: ["locations"] as const,
		lists: () => [...queryKeys.locations.all, "list"] as const,
		list: (filters: Record<string, any>) =>
			[...queryKeys.locations.lists(), filters] as const,
		details: () => [...queryKeys.locations.all, "detail"] as const,
		detail: (id: string) => [...queryKeys.locations.details(), id] as const,
	},

	// Business attributes related queries
	businessAttributes: {
		all: ["businessAttributes"] as const,
		lists: () => [...queryKeys.businessAttributes.all, "list"] as const,
		list: (filters: Record<string, any>) =>
			[...queryKeys.businessAttributes.lists(), filters] as const,
	},

	// Clients related queries
	clients: {
		all: ["clients"] as const,
		lists: () => [...queryKeys.clients.all, "list"] as const,
		list: (filters: Record<string, any>) =>
			[...queryKeys.clients.lists(), filters] as const,
		details: () => [...queryKeys.clients.all, "detail"] as const,
		detail: (id: string) => [...queryKeys.clients.details(), id] as const,
	},
} as const;
