import <PERSON> from "papaparse";
import * as XLSX from "xlsx";

export interface FileParseResult {
	headers: string[];
	data: any[][];
	error?: string;
}

export interface ExcelSheetsResult {
	sheets: string[];
	error?: string;
}

/**
 * Parse CSV file and extract headers and data
 */
export const parseCSVFile = (file: File): Promise<FileParseResult> => {
	return new Promise((resolve) => {
		Papa.parse(file, {
			header: false,
			skipEmptyLines: true,
			complete: (results) => {
				if (results.errors.length > 0) {
					resolve({
						headers: [],
						data: [],
						error: results.errors[0].message,
					});
					return;
				}

				const data = results.data as string[][];
				if (data.length === 0) {
					resolve({
						headers: [],
						data: [],
						error: "File is empty",
					});
					return;
				}

				// First row contains headers
				const headers = data[0].filter(
					(header) => header && header.trim() !== ""
				);
				const dataRows = data.slice(1);

				resolve({
					headers,
					data: dataRows,
				});
			},
			error: (error) => {
				resolve({
					headers: [],
					data: [],
					error: error.message,
				});
			},
		});
	});
};

/**
 * Get all sheet names from an Excel file
 */
export const getExcelSheets = (file: File): Promise<ExcelSheetsResult> => {
	return new Promise((resolve) => {
		const reader = new FileReader();

		reader.onload = (e) => {
			try {
				const data = new Uint8Array(e.target?.result as ArrayBuffer);
				const workbook = XLSX.read(data, { type: "array" });

				if (workbook.SheetNames.length === 0) {
					resolve({
						sheets: [],
						error: "No worksheets found in the file",
					});
					return;
				}

				resolve({
					sheets: workbook.SheetNames,
				});
			} catch (error) {
				resolve({
					sheets: [],
					error:
						error instanceof Error
							? error.message
							: "Failed to read Excel file",
				});
			}
		};

		reader.onerror = () => {
			resolve({
				sheets: [],
				error: "Failed to read file",
			});
		};

		reader.readAsArrayBuffer(file);
	});
};

/**
 * Parse Excel file and extract headers and data from a specific sheet
 */
export const parseExcelFile = (
	file: File,
	sheetName?: string
): Promise<FileParseResult> => {
	return new Promise((resolve) => {
		const reader = new FileReader();

		reader.onload = (e) => {
			try {
				const data = new Uint8Array(e.target?.result as ArrayBuffer);
				const workbook = XLSX.read(data, { type: "array" });

				// Use specified sheet or first sheet
				const targetSheetName = sheetName || workbook.SheetNames[0];
				if (!targetSheetName || !workbook.Sheets[targetSheetName]) {
					resolve({
						headers: [],
						data: [],
						error: sheetName
							? `Sheet "${sheetName}" not found in the file`
							: "No worksheets found in the file",
					});
					return;
				}

				const worksheet = workbook.Sheets[targetSheetName];

				// Convert to array of arrays
				const jsonData = XLSX.utils.sheet_to_json(worksheet, {
					header: 1,
					defval: "",
				}) as string[][];

				if (jsonData.length === 0) {
					resolve({
						headers: [],
						data: [],
						error: "Worksheet is empty",
					});
					return;
				}

				// First row contains headers
				const headers = jsonData[0].filter(
					(header) => header && header.toString().trim() !== ""
				);
				const dataRows = jsonData.slice(1);

				resolve({
					headers,
					data: dataRows,
				});
			} catch (error) {
				resolve({
					headers: [],
					data: [],
					error:
						error instanceof Error
							? error.message
							: "Failed to parse Excel file",
				});
			}
		};

		reader.onerror = () => {
			resolve({
				headers: [],
				data: [],
				error: "Failed to read file",
			});
		};

		reader.readAsArrayBuffer(file);
	});
};

/**
 * Parse file based on its extension
 */
export const parseFile = async (
	file: File,
	sheetName?: string
): Promise<FileParseResult> => {
	const fileName = file.name.toLowerCase();

	if (fileName.endsWith(".csv")) {
		return parseCSVFile(file);
	} else if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
		return parseExcelFile(file, sheetName);
	} else {
		return {
			headers: [],
			data: [],
			error: "Unsupported file format. Please upload a CSV or Excel file.",
		};
	}
};

/**
 * Get file headers only (for quick preview)
 */
export const getFileHeaders = async (
	file: File,
	sheetName?: string
): Promise<string[]> => {
	const result = await parseFile(file, sheetName);
	if (result.error) {
		console.error("Error parsing file:", result.error);
		return [];
	}
	return result.headers;
};

/**
 * Get all available sheets from a file (Excel only, returns empty array for CSV)
 */
export const getFileSheets = async (file: File): Promise<string[]> => {
	const fileName = file.name.toLowerCase();

	if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
		const result = await getExcelSheets(file);
		if (result.error) {
			console.error("Error getting sheets:", result.error);
			return [];
		}
		return result.sheets;
	}

	// CSV files don't have sheets
	return [];
};
