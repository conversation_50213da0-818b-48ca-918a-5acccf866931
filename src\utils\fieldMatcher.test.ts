// Test file for field matching logic
// This would be a proper test file in a real testing environment

interface MigraniumField {
	key: string;
	label: string;
	type: string;
}

// Mock function to test the matching logic
const findBestMatch = (csvHeader: string, availableFields: MigraniumField[]): string => {
	const normalizedHeader = csvHeader.toLowerCase().trim();
	
	// Define matching patterns for common fields
	const matchingPatterns: { [key: string]: string[] } = {
		firstName: ['first name', 'firstname', 'first_name', 'fname', 'given name', 'givenname'],
		lastName: ['last name', 'lastname', 'last_name', 'lname', 'surname', 'family name', 'familyname'],
		email: ['email', 'email address', 'e-mail', 'e_mail', 'mail', 'email_address'],
		phone: ['phone', 'phone number', 'phonenumber', 'phone_number', 'mobile', 'cell', 'telephone', 'tel'],
	};

	// First, try exact matches with patterns
	for (const [fieldKey, patterns] of Object.entries(matchingPatterns)) {
		if (patterns.includes(normalizedHeader)) {
			const field = availableFields.find(f => f.key === fieldKey);
			if (field) return field.key;
		}
	}

	// Then try partial matches
	for (const [fieldKey, patterns] of Object.entries(matchingPatterns)) {
		for (const pattern of patterns) {
			if (normalizedHeader.includes(pattern) || pattern.includes(normalizedHeader)) {
				const field = availableFields.find(f => f.key === fieldKey);
				if (field) return field.key;
			}
		}
	}

	// Try matching with available field labels (case-insensitive)
	const exactLabelMatch = availableFields.find(field => 
		field.label.toLowerCase() === normalizedHeader
	);
	if (exactLabelMatch) return exactLabelMatch.key;

	// Try partial label matches
	const partialLabelMatch = availableFields.find(field => {
		const fieldLabel = field.label.toLowerCase();
		return fieldLabel.includes(normalizedHeader) || normalizedHeader.includes(fieldLabel);
	});
	if (partialLabelMatch) return partialLabelMatch.key;

	// No match found
	return "";
};

// Test cases
const testFields: MigraniumField[] = [
	{ key: "firstName", label: "First Name", type: "text" },
	{ key: "lastName", label: "Last Name", type: "text" },
	{ key: "email", label: "Email", type: "email" },
	{ key: "phone", label: "Phone", type: "phone" },
];

// Test scenarios
console.log("Testing field matching logic:");
console.log("First Name ->", findBestMatch("First Name", testFields)); // Should match firstName
console.log("firstname ->", findBestMatch("firstname", testFields)); // Should match firstName
console.log("Last Name ->", findBestMatch("Last Name", testFields)); // Should match lastName
console.log("Email ->", findBestMatch("Email", testFields)); // Should match email
console.log("Phone Number ->", findBestMatch("Phone Number", testFields)); // Should match phone
console.log("Unknown Field ->", findBestMatch("Unknown Field", testFields)); // Should return ""

export { findBestMatch };
