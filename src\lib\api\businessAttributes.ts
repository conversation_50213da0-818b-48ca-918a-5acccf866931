import { apiClient } from "./clients";
import type {
	BusinessAttributesResponse,
	BusinessAttributesFilters,
} from "@/types/businessAttributes";

const BUSINESS_ATTRIBUTES_ENDPOINTS = {
	base: "/core/api/v1/business-attributes",
} as const;

export const businessAttributesApi = {
	// Get business attributes with pagination and filters
	getBusinessAttributes: async (
		filters: BusinessAttributesFilters = {}
	): Promise<BusinessAttributesResponse> => {
		const params = new URLSearchParams();

		// Add pagination parameters
		if (filters.page !== undefined) {
			params.append("page", filters.page.toString());
		}
		if (filters.per_page !== undefined) {
			params.append("per_page", filters.per_page.toString());
		}

		// Add filter parameters
		if (filters.search) {
			params.append("search", filters.search);
		}
		if (filters.type) {
			params.append("type", filters.type);
		}
		if (filters.is_required !== undefined) {
			params.append("is_required", filters.is_required.toString());
		}
		if (filters.show_in_list !== undefined) {
			params.append("show_in_list", filters.show_in_list.toString());
		}

		const queryString = params.toString();
		const url = queryString
			? `${BUSINESS_ATTRIBUTES_ENDPOINTS.base}?${queryString}`
			: BUSINESS_ATTRIBUTES_ENDPOINTS.base;

		const response = await apiClient.get(url);
		return response.data;
	},
};
