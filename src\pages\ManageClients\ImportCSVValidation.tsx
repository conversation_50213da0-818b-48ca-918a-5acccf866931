import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, Trash2, MinusCircle } from "lucide-react";
import { SelectSheetInfoCard } from "@/components/common/SelectSheetInfoCard";
import { ImportSuccessCard } from "@/components/common/ImportSuccessCard";
import { parseFile } from "@/utils/fileParser";
import { InputText } from "@/components/common/InputText/InputText";

// Define interfaces for mapping data
interface MappingRow {
	id: number;
	excel: string;
	migranium: string;
	type: string;
	isCreatingNew?: boolean;
	newFieldLabel?: string;
}

interface MappingData {
	mappingRows: MappingRow[];
	selectedSheet: string;
	availableSheets: string[];
}

interface ValidationRow {
	id: number;
	excelCell: string;
	[key: string]: any; // Dynamic fields based on mapping
	errors: string[]; // Array of field names with errors
	visualErrors: string[]; // Array of field names with visual errors (red borders)
}

interface ValidationData {
	readyToImport: number;
	missingDataConflicts: number;
	duplicateDataConflicts: number;
	showMissingData: boolean;
	showDuplicateData: boolean;
	validationRows: ValidationRow[];
}

interface ImportState {
	isImporting: boolean;
	isSuccess: boolean;
	importedCount: number;
}

interface ImportCSVValidationProps {
	uploadedFile: File | null;
	mappingData: MappingData | null;
	onBack: () => void;
	onTest: () => void;
	onImport: () => void;
	onPopulateData?: () => void;
	onImportAgain?: () => void;
	onDone?: () => void;
	onImportSuccess?: (isSuccessful: boolean) => void;
}

export default function ImportCSVValidation({
	uploadedFile,
	mappingData,
	onBack,
	onTest,
	onImport,
	onPopulateData,
	onImportAgain,
	onDone,
	onImportSuccess,
}: ImportCSVValidationProps) {
	const [validationData, setValidationData] = useState<ValidationData>({
		readyToImport: 0,
		missingDataConflicts: 0,
		duplicateDataConflicts: 0,
		showMissingData: true,
		showDuplicateData: false,
		validationRows: [],
	});

	const [isValidating, setIsValidating] = useState(false);

	const [importState, setImportState] = useState<ImportState>({
		isImporting: false,
		isSuccess: false,
		importedCount: 0,
	});

	const handleImport = () => {
		setImportState((prev) => ({ ...prev, isImporting: true }));
		setTimeout(() => {
			setImportState({
				isImporting: false,
				isSuccess: true,
				importedCount: validationData.readyToImport,
			});
			onImportSuccess?.(true);
		}, 2000);
	};

	// Validate data when mapping data changes
	useEffect(() => {
		const validateData = async () => {
			if (
				!uploadedFile ||
				!mappingData ||
				mappingData.mappingRows.length === 0
			) {
				return;
			}

			setIsValidating(true);
			try {
				// Parse the file data
				const fileResult = await parseFile(
					uploadedFile,
					mappingData.selectedSheet || undefined
				);
				if (fileResult.error || fileResult.data.length === 0) {
					console.error("Error parsing file:", fileResult.error);
					return;
				}

				const validationRows: ValidationRow[] = [];
				let missingDataCount = 0;
				let readyToImportCount = 0;

				// Get mapped fields (only those that have been mapped)
				const mappedFields = mappingData.mappingRows.filter(
					(row) => row.migranium && row.migranium.trim() !== ""
				);

				// Required fields that must be filled
				const requiredFields = ["firstName", "lastName", "email"];

				// Validate each data row
				fileResult.data.forEach((dataRow, index) => {
					const validationRow: ValidationRow = {
						id: index + 1,
						excelCell: `B${index + 2}`, // Assuming B column, starting from row 2
						errors: [],
						visualErrors: [],
					};

					let hasErrors = false;

					// Check each mapped field
					mappedFields.forEach((mappingRow, mappingIndex) => {
						const cellValue = dataRow[mappingIndex];
						const fieldKey = mappingRow.migranium;

						// Store the value in the validation row
						validationRow[fieldKey] = cellValue || "";

						// Check if required field is empty
						if (
							requiredFields.includes(fieldKey) &&
							(!cellValue || cellValue.toString().trim() === "")
						) {
							validationRow.errors.push(fieldKey);
							validationRow.visualErrors.push(fieldKey);
							hasErrors = true;
						}

						// Additional validation based on field type
						if (cellValue && cellValue.toString().trim() !== "") {
							switch (mappingRow.type) {
								case "email":
									const emailRegex =
										/^[^\s@]+@[^\s@]+\.[^\s@]+$/;
									if (
										!emailRegex.test(cellValue.toString())
									) {
										validationRow.errors.push(fieldKey);
										validationRow.visualErrors.push(
											fieldKey
										);
										hasErrors = true;
									}
									break;
								case "phone":
									// Basic phone validation
									const phoneRegex = /^[\d\s\-\+\(\)]+$/;
									if (
										!phoneRegex.test(cellValue.toString())
									) {
										validationRow.errors.push(fieldKey);
										validationRow.visualErrors.push(
											fieldKey
										);
										hasErrors = true;
									}
									break;
								case "date":
									// Basic date validation
									if (
										isNaN(Date.parse(cellValue.toString()))
									) {
										validationRow.errors.push(fieldKey);
										validationRow.visualErrors.push(
											fieldKey
										);
										hasErrors = true;
									}
									break;
							}
						}
					});

					validationRows.push(validationRow);

					if (hasErrors) {
						missingDataCount++;
					} else {
						readyToImportCount++;
					}
				});

				setValidationData({
					readyToImport: readyToImportCount,
					missingDataConflicts: missingDataCount,
					duplicateDataConflicts: 0, // TODO: Implement duplicate detection
					showMissingData: missingDataCount > 0,
					showDuplicateData: false,
					validationRows: validationRows,
				});
			} catch (error) {
				console.error("Error validating data:", error);
			} finally {
				setIsValidating(false);
			}
		};

		validateData();
	}, [uploadedFile, mappingData]);

	// Recalculate validation stats when validation rows change
	useEffect(() => {
		const rowsWithErrors = validationData.validationRows.filter(
			(row) => row.errors.length > 0
		);
		const rowsReady = validationData.validationRows.filter(
			(row) => row.errors.length === 0
		);

		setValidationData((prev) => ({
			...prev,
			missingDataConflicts: rowsWithErrors.length,
			readyToImport: rowsReady.length,
			showMissingData:
				rowsWithErrors.length > 0 ? prev.showMissingData : false,
		}));
	}, [validationData.validationRows]);

	const hasNoConflicts =
		validationData.missingDataConflicts === 0 &&
		validationData.duplicateDataConflicts === 0;

	// Check if all validation errors are resolved (for import button state)
	const allErrorsResolved = validationData.validationRows.every(
		(row) => row.errors.length === 0
	);

	// Get rows with missing data for display
	const missingDataRows = validationData.validationRows.filter(
		(row) => row.errors.length > 0
	);

	// Get mapped field information for display headers
	const getMappedFields = () => {
		if (!mappingData) return [];
		return mappingData.mappingRows.filter(
			(row) => row.migranium && row.migranium.trim() !== ""
		);
	};

	const mappedFields = getMappedFields();

	// Handle field value updates
	const handleFieldUpdate = (
		rowId: number,
		fieldKey: string,
		newValue: string
	) => {
		setValidationData((prev) => ({
			...prev,
			validationRows: prev.validationRows.map((row) => {
				if (row.id === rowId) {
					const updatedRow = { ...row, [fieldKey]: newValue };

					// Re-validate this specific field
					const requiredFields = ["firstName", "lastName", "email"];
					let newErrors = [...row.errors];
					let newVisualErrors = [...row.visualErrors];

					// Remove this field from visual errors when user starts typing (removes red border)
					newVisualErrors = newVisualErrors.filter(
						(error) => error !== fieldKey
					);

					// Remove from logical errors and re-evaluate
					newErrors = newErrors.filter((error) => error !== fieldKey);

					// Only add back to errors if field is empty and required, or if validation fails
					if (!newValue || newValue.trim() === "") {
						// Add to both errors and visual errors if it's a required field and empty
						if (requiredFields.includes(fieldKey)) {
							newErrors.push(fieldKey);
							newVisualErrors.push(fieldKey);
						}
					} else {
						// Field has content, validate format
						const mappingRow = mappingData?.mappingRows.find(
							(m) => m.migranium === fieldKey
						);
						if (mappingRow) {
							let hasFormatError = false;
							switch (mappingRow.type) {
								case "email":
									const emailRegex =
										/^[^\s@]+@[^\s@]+\.[^\s@]+$/;
									if (!emailRegex.test(newValue)) {
										hasFormatError = true;
									}
									break;
								case "phone":
									const phoneRegex = /^[\d\s\-\+\(\)]+$/;
									if (!phoneRegex.test(newValue)) {
										hasFormatError = true;
									}
									break;
								case "date":
									if (isNaN(Date.parse(newValue))) {
										hasFormatError = true;
									}
									break;
							}

							if (hasFormatError) {
								newErrors.push(fieldKey);
								newVisualErrors.push(fieldKey);
							}
						}
					}

					updatedRow.errors = newErrors;
					updatedRow.visualErrors = newVisualErrors;
					return updatedRow;
				}
				return row;
			}),
		}));
	};

	// Handle row deletion
	const handleDeleteRow = (rowId: number) => {
		setValidationData((prev) => ({
			...prev,
			validationRows: prev.validationRows.filter(
				(row) => row.id !== rowId
			),
		}));
	};

	if (importState.isSuccess) {
		return (
			<div className="flex w-full flex-col items-center justify-center gap-4 px-6 py-8">
				<ImportSuccessCard
					patientsCount={importState.importedCount}
					onImportAgain={onImportAgain || (() => {})}
					onDone={onDone || (() => {})}
				/>
			</div>
		);
	}

	return (
		<div className="flex min-h-[calc(100vh-200px)] w-full flex-col">
			<div className="flex flex-1 flex-col items-start justify-start gap-4 px-6 py-4">
				<SelectSheetInfoCard
					onNext={onPopulateData || (() => {})}
					selectedSheet={
						mappingData?.selectedSheet || "No sheet selected"
					}
					sheets={mappingData?.availableSheets || []}
					buttonText="Populate data"
				/>

				<div className="flex w-full flex-col items-start justify-start gap-4">
					{isValidating ? (
						<div className="flex w-full items-center justify-start gap-3 rounded-lg border-b border-gray-200 bg-white p-4">
							<div className="flex flex-1 flex-col items-start justify-start gap-1">
								<div className="text-sm font-medium text-blue-600">
									Validating data...
								</div>
								<div className="text-xs text-gray-500">
									Please wait while we validate your data.
								</div>
							</div>
						</div>
					) : validationData.readyToImport > 0 ? (
						<div className="flex w-full items-center justify-start gap-3 rounded-lg border-b border-gray-200 bg-white p-4">
							<div className="flex flex-1 flex-col items-start justify-start gap-1">
								<div className="text-sm font-medium text-green-600">
									{validationData.readyToImport} data is ready
									to import
								</div>
								<div className="text-xs text-gray-500">
									These data is perfectly fine to import in
									system.
								</div>
							</div>
						</div>
					) : null}
					{validationData.missingDataConflicts > 0 && (
						<div className="flex w-full flex-col items-start justify-start gap-3 rounded-lg bg-white p-4">
							<div className="flex w-full items-center justify-between border-b border-gray-200 pb-3">
								<div className="flex flex-1 flex-col items-start justify-start gap-1">
									<div className="text-sm font-medium text-red-600">
										{validationData.missingDataConflicts}{" "}
										Conflicts: Missing Data
									</div>
									<div className="text-xs text-gray-500">
										Need to resolve before importing these
										data to system.
									</div>
								</div>
								<div className="flex items-center gap-2">
									<button
										onClick={() =>
											setValidationData((prev) => ({
												...prev,
												showMissingData: false,
											}))
										}
										className="flex h-9 items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-medium text-gray-900 hover:bg-gray-50"
									>
										<Trash2 className="h-3 w-3" />
										Discard conflicted data
									</button>
									<button
										onClick={() =>
											setValidationData((prev) => ({
												...prev,
												showMissingData:
													!prev.showMissingData,
											}))
										}
										className={`flex h-9 w-9 items-center justify-center rounded-md transition-transform hover:bg-gray-100 ${
											validationData.showMissingData
												? "rotate-180"
												: ""
										}`}
									>
										<ChevronDown className="h-3 w-3" />
									</button>
								</div>
							</div>

							{validationData.showMissingData && (
								<div className="w-full overflow-x-auto">
									<table className="w-full border-collapse">
										<thead>
											<tr className="border-b border-gray-200">
												<th className="w-12 px-2 py-3 text-left">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
														#
													</div>
												</th>
												<th className="w-20 px-2 py-3 text-left">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
														Excel Cell
													</div>
												</th>
												{mappedFields.map((field) => (
													<th
														key={field.id}
														className="min-w-[150px] px-2 py-3 text-left"
													>
														<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
															{field.excel}{" "}
															{[
																"firstName",
																"lastName",
																"email",
															].includes(
																field.migranium
															)
																? "*"
																: ""}
														</div>
													</th>
												))}
												<th className="w-12 px-2 py-3 text-center">
													<div className="font-['Inter'] text-xs leading-none font-medium text-gray-500">
														{/* Icon header - no text */}
													</div>
												</th>
											</tr>
										</thead>
										<tbody>
											{missingDataRows.map((row) => (
												<tr
													key={row.id}
													className="border-t border-gray-200"
												>
													<td className="w-12 px-2 py-3">
														<div className="font-['Inter'] text-sm leading-tight font-medium text-gray-900">
															{row.id}
														</div>
													</td>
													<td className="w-20 px-2 py-3">
														<div className="font-['Inter'] text-sm leading-tight font-normal text-gray-500">
															{row.excelCell}
														</div>
													</td>
													{mappedFields.map(
														(field) => {
															const hasVisualError =
																row.visualErrors.includes(
																	field.migranium
																);
															const fieldValue =
																row[
																	field
																		.migranium
																] || "";
															// Keep fields editable for better UX - users can always modify their input

															return (
																<td
																	key={
																		field.id
																	}
																	className="min-w-[150px] px-2 py-3"
																>
																	<div className="relative">
																		<input
																			type="text"
																			value={
																				fieldValue
																			}
																			onChange={(
																				e
																			) =>
																				handleFieldUpdate(
																					row.id,
																					field.migranium,
																					e
																						.target
																						.value
																				)
																			}
																			className={`h-9 w-full rounded-md border px-3 py-2 text-xs focus:ring-2 focus:ring-blue-500 focus:outline-none ${
																				hasVisualError
																					? "border-red-500 bg-red-50 text-red-500"
																					: "border-gray-300 text-gray-900"
																			}`}
																			placeholder={`Enter ${field.excel.toLowerCase()}`}
																		/>
																	</div>
																</td>
															);
														}
													)}
													<td className="w-12 px-2 py-3 text-center">
														<button
															onClick={() =>
																handleDeleteRow(
																	row.id
																)
															}
															className="flex h-6 w-6 items-center justify-center text-gray-500 transition-colors hover:text-red-500"
														>
															<MinusCircle className="h-4 w-4" />
														</button>
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							)}
						</div>
					)}
					{/* Duplicate Data Conflicts */}
					{validationData.duplicateDataConflicts > 0 && (
						<div className="inline-flex items-center justify-start gap-3 self-stretch border-t border-gray-200 pb-3">
							<div className="inline-flex flex-1 flex-col items-start justify-start gap-1">
								<div className="justify-center self-stretch font-['Inter'] text-sm leading-tight font-medium text-red-600">
									{validationData.duplicateDataConflicts}{" "}
									Conflicts: Duplicate Data
								</div>
								<div className="justify-start self-stretch font-['Inter'] text-xs leading-none font-normal text-gray-500">
									Need to resolve before importing these data
									to system.
								</div>
							</div>
							<button
								onClick={() =>
									setValidationData((prev) => ({
										...prev,
										showDuplicateData: false,
									}))
								}
								className="flex h-9 w-32 items-center justify-center gap-2 rounded-md border border-gray-300 bg-white px-4 py-2"
							>
								<Trash2 className="h-3 w-3" />
								<div className="justify-center font-['Inter'] text-xs leading-none font-medium text-gray-900">
									Discard conflicted data
								</div>
							</button>
							<button
								onClick={() =>
									setValidationData((prev) => ({
										...prev,
										showDuplicateData:
											!prev.showDuplicateData,
									}))
								}
								className={`flex h-9 w-9 items-center justify-center gap-2 rounded-md px-4 py-2 transition-transform ${
									validationData.showDuplicateData
										? "rotate-180"
										: ""
								}`}
							>
								<ChevronDown className="h-3 w-3" />
							</button>
						</div>
					)}
				</div>

				<div className="mt-auto flex w-full items-center justify-between gap-3 bg-white px-6 py-4">
					<button
						onClick={onBack}
						className="flex h-9 items-center justify-center gap-2 rounded-md px-4 py-2 text-xs font-medium text-gray-900 hover:bg-gray-100"
					>
						Back
					</button>
					<div className="flex items-center gap-3">
						<button
							onClick={onTest}
							className="flex h-9 items-center justify-center gap-2 rounded-md border border-[#005893] bg-white px-4 py-2 text-xs font-medium text-[#005893] hover:bg-blue-50"
						>
							Test
						</button>
						<button
							onClick={handleImport}
							disabled={
								importState.isImporting || !allErrorsResolved
							}
							className="flex h-9 items-center justify-center gap-2 rounded-md bg-[#005893] px-4 py-2 text-xs font-medium text-white hover:bg-[#004a7a] disabled:cursor-not-allowed disabled:opacity-50"
						>
							{importState.isImporting
								? "Importing..."
								: "Import"}
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
